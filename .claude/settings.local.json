{"permissions": {"allow": ["<PERSON><PERSON>(python test:*)", "<PERSON><PERSON>(python3:*)", "Bash(pip install:*)", "Bash(pip3 install:*)", "Bash(conda activate:*)", "<PERSON><PERSON>(source:*)", "mcp__sequential-thinking__sequentialthinking", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(rm:*)"]}, "enabledMcpjsonServers": ["context7", "sequential-thinking"]}