# Documentation Directory

This directory contains all documentation for the TradingAgents project.

## Core Documentation

### Setup and Configuration
- **`CLAUDE.md`** - Main documentation for Claude Code integration
  - Development commands and setup instructions
  - Architecture overview and component descriptions
  - API usage examples and configuration

### System Configuration Guides
- **`TradingAgents_自定义LLM配置完整指南.md`** - Complete custom LLM configuration guide
- **`TradingAgents_自定义LLM_Endpoint配置指南.md`** - Custom endpoint configuration
- **`TradingAgents_自定义endpoint的OPENAI_API_KEY说明.md`** - OpenAI API key explanation for custom endpoints

### System Analysis and Architecture
- **`TradingAgents_技术深度解析与上手指南.md`** - Technical deep dive and getting started guide
- **`TradingAgents_Agent技术架构深度分析.md`** - **🆕 Agent技术架构深度分析** - 全面分析多Agent协作、状态管理、辩论机制等核心技术创新
- **`TradingAgents_双模型架构_深度思考vs快速响应使用场景分析.md`** - Dual-model architecture analysis
- **`TradingAgents_系统测试指南.md`** - System testing guide

### Dependency and Configuration Analysis
- **`TradingAgents_依赖分析_未使用库识别.md`** - Unused library dependency analysis
- **`TradingAgents_依赖分析修正_akshare和tushare真实覆盖范围.md`** - Corrected analysis of akshare and tushare coverage
- **`TradingAgents_FinnHub依赖性分析_是否必需及替代方案.md`** - FinnHub dependency analysis and alternatives

### System Modifications and Solutions
- **`TradingAgents_CLI修改说明_直接使用配置文件.md`** - CLI modification to use configuration files directly
- **`TradingAgents_Embedding模型配置解决方案.md`** - Embedding model configuration solution

## Navigation

For quick reference:
- **Setup**: Start with `CLAUDE.md`
- **Configuration**: See the `自定义LLM配置` guides
- **Architecture**: Read the `技术深度解析` document
- **Testing**: Check the `系统测试指南`
- **Troubleshooting**: Refer to specific analysis documents

## Language Note

Most documentation is in Chinese (中文) as per the original project development context.