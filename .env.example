# .env配置示例
# TradingAgents 自定义LLM Endpoint 环境变量配置

# =============================================================================
# LLM服务配置
# =============================================================================

# 您的自定义LLM服务配置
TRADINGAGENTS_LLM_PROVIDER=openai
TRADINGAGENTS_BACKEND_URL=https://your-custom-endpoint.com/v1

# API认证密钥 (使用标准的OPENAI_API_KEY环境变量)
OPENAI_API_KEY=your_custom_api_key_here

# =============================================================================
# 模型选择配置
# =============================================================================

# 深度思考模型 - 用于复杂决策(研究经理、风险经理)
TRADINGAGENTS_DEEP_THINK_LLM=deepseek-r1

# 快速响应模型 - 用于分析和执行任务(分析师、研究员、交易员等)
TRADINGAGENTS_QUICK_THINK_LLM=gemini-2.5-flash

# Embedding模型 - 用于记忆系统的向量化处理
# 选项1: 使用自定义endpoint的embedding (如果支持)
# TRADINGAGENTS_EMBEDDING_MODEL=text-embedding-3-large

# 选项2: 使用本地Ollama提供embedding服务 (推荐)
TRADINGAGENTS_EMBEDDING_MODEL=nomic-embed-text:latest
TRADINGAGENTS_EMBEDDING_BACKEND_URL=http://localhost:10000

# =============================================================================
# 其他可选配置
# =============================================================================

# 辩论轮数配置
TRADINGAGENTS_MAX_DEBATE_ROUNDS=2
TRADINGAGENTS_MAX_RISK_DISCUSS_ROUNDS=2

# 系统配置
TRADINGAGENTS_MAX_RECUR_LIMIT=150
TRADINGAGENTS_ONLINE_TOOLS=true

# 金融数据API (可选)
FINNHUB_API_KEY=your_finnhub_key_here

# =============================================================================
# 替代模型配置方案
# =============================================================================

# 方案1: 性能优先
# TRADINGAGENTS_DEEP_THINK_LLM=deepseek-r1
# TRADINGAGENTS_QUICK_THINK_LLM=gemini-2.5-flash
# TRADINGAGENTS_EMBEDDING_MODEL=text-embedding-3-large

# 方案2: 成本平衡  
# TRADINGAGENTS_DEEP_THINK_LLM=qwen3-235b-a22b
# TRADINGAGENTS_QUICK_THINK_LLM=gemini-2.5-flash
# TRADINGAGENTS_EMBEDDING_MODEL=text-embedding-3-small

# 方案3: Google生态
# TRADINGAGENTS_DEEP_THINK_LLM=gemini-2.5-pro
# TRADINGAGENTS_QUICK_THINK_LLM=gemini-2.5-flash
# TRADINGAGENTS_EMBEDDING_MODEL=text-embedding-3-small